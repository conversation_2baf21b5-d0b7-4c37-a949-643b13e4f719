!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("index",[],t):"object"==typeof exports?exports.index=t():e.index=t()}(this,(()=>{return e={0:(e,t,o)=>{const s=o(216),n=o(599),r=o(719);e.exports=class{constructor(){this.faceTecRepository=new s}async execute({idScanResult:e,deviceKey:t,additionalHeaders:o={},onProgress:s=null}){const n=performance.now();try{r.logMessage("Starting PostIDScanOnlyUseCase execution"),r.logMessage("Preparing scan data...");const c=this.prepareScanData(e);r.logData("Scan Data Keys",Object.keys(c)),r.logMessage("Preparing headers...");const a=this.prepareHeaders(e,t,o);r.logData("Request Headers",Object.keys(a)),r.logMessage("Validating scan data..."),this.faceTecRepository.validateScanData(c),r.logSuccess("Scan data validation passed"),r.logMessage("Submitting to repository...");const i=await this.faceTecRepository.submitIDScan(c,a,s);r.logMessage("Processing response...");const l=this.processResponse(i),u=performance.now();return r.logPerformance("PostIDScanOnlyUseCase.execute",n,u),r.logSuccess(`UseCase completed successfully: ${l.success}`),l}catch(e){const t=performance.now();throw r.logPerformance("PostIDScanOnlyUseCase.execute (failed)",n,t),r.logError("PostIDScanOnlyUseCase - execute error",e),e}}prepareScanData(e){const t={idScan:e.idScan,enableConfirmInfo:!0};return e.frontImages&&e.frontImages[0]&&(t.idScanFrontImage=e.frontImages[0]),e.backImages&&e.backImages[0]&&(t.idScanBackImage=e.backImages[0]),t}prepareHeaders(e,t,o){const s={};return t&&(s["X-Device-Key"]=t),e.sessionId&&(s["X-User-Agent"]=FaceTecSDK.createFaceTecAPIUserAgentString(e.sessionId)),o.Authorization&&(s.Authorization=o.Authorization),o["X-Session-Id"]&&(s["X-Session-Id"]=o["X-Session-Id"]),o["X-Ekyc-Token"]&&(s["X-Ekyc-Token"]=o["X-Ekyc-Token"]),o.correlationid&&(s.correlationid=o.correlationid),s["X-Tid"]=n.getUniqueId(),s}processResponse(e){return{success:!0===e.wasProcessed&&!1===e.error,scanResultBlob:e.scanResultBlob,originalResponse:e.originalResponse,errorMessage:e.errorMessage}}}},103:e=>{e.exports=class{constructor(e){this.data=e}getToken(){return this.data?.token||null}getEkycToken(){return this.data?.data?.ekycToken?this.data.data.ekycToken:this.data?.ekycToken||null}getExpiresAt(){return this.data?.expiresAt||null}getCode(){return this.data?.code||null}getDescription(){return this.data?.description||null}isValid(){return!!this.getEkycToken()}toJSON(){return this.data}}},161:(e,t,o)=>{const s=o(103);e.exports=class{constructor(e){this.authApiDataSource=e}async getSessionToken(e={}){const t=await this.authApiDataSource.getSessionToken(e);return new s(t)}async getFaceTecSessionTokenWithEkycToken(e={}){const t=await this.authApiDataSource.getFaceTecSessionTokenWithEkycToken(e);return new s(t)}}},189:e=>{e.exports=class{constructor(e,t="USD"){this.amount=e,this.currencyCode=t}format(){return new Intl.NumberFormat("en-US",{style:"currency",currency:this.currencyCode}).format(this.amount)}getAmount(){return this.amount}getCurrencyCode(){return this.currencyCode}}},216:(e,t,o)=>{const s=o(518);e.exports=class{constructor(){this.idScanDataSource=new s}async submitIDScan(e,t={},o=null){try{return await this.idScanDataSource.postIDScanOnly(e,t,o)}catch(e){throw console.error("FaceTecRepository - submitIDScan error:",e),e}}validateScanData(e){if(!e)throw new Error("Scan data is required");if(!e.idScan)throw new Error("ID scan data is required");return!0}}},347:(e,t,o)=>{const s=o(592),n=o(863),r=o(995),c=o(985),a=o(161),i=o(548),{TokenStorage:l}=o(411),u=o(382),d=new a(new i),g=new s,S=new n,p=new r(d),h=new c(d),y=o(955);e.exports={formatCurrency:(e,t="USD")=>g.execute(e,t),greet:e=>S.execute(e),getSessionToken:async(e={},t=!0)=>{try{e["X-Session-Id"]&&l.storeSessionId(e["X-Session-Id"]);const o=await p.execute(e),s=o.toJSON();if(t){const e=o.getEkycToken();e&&l.storeEkycToken(e)}return s}catch(e){throw console.error("Error getting session token:",e),e}},getStoredEkycToken:()=>l.getEkycToken(),clearEkycToken:()=>l.removeEkycToken(),getFaceTecSessionTokenWithEkycToken:async(e={},t=!0)=>{try{const o=l.getSessionId();o&&!e["X-Session-Id"]&&(e={...e,"X-Session-Id":o});const s=(await h.execute(e)).toJSON();if(s.faceTecInitialized=!1,t&&s&&"CUS-KYC-1000"===s.code&&s.data&&s.data.deviceKey&&s.data.encryptionKey)try{await u.initializeFaceTec(s.data.deviceKey,s.data.encryptionKey),console.log("FaceTec SDK initialized successfully"),s.faceTecInitialized=!0}catch(e){console.error("Error initializing FaceTec SDK:",e),s.faceTecError=e.message||"Failed to initialize FaceTec SDK"}return s}catch(e){throw console.error("Error getting FaceTec session token with eKYC token:",e),e}},performPhotoIDScan:async(e={},t=null,s=null)=>{try{if(!t)throw new Error("deviceKey parameter is required for Photo ID Scan");if(!s.faceTecInitialized)throw new Error("FaceTec SDK not initialized properly");const n=await u.loadFaceTecSDK();n.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),n.setImagesDirectory("/core-sdk/FaceTec_images");const r={onComplete:(e,t,o)=>({sessionResult:e,idScanResult:t,networkResponseStatus:o})},c={"X-Session-Id":e["X-Session-Id"]||s.data?.sessionId,"X-Ekyc-Token":s.data?.ekycToken||l.getEkycToken(),correlationid:e.correlationid||o(411).UuidGenerator.getUniqueId()},a=new y(s.data.sessionFaceTec,r,t,c);return new Promise(((e,t)=>{r.onComplete=(o,s,n)=>{a.isSuccess()?e({sessionResult:o,idScanResult:s,networkResponseStatus:n}):t(new Error("ID scan failed"))}}))}catch(e){throw console.error("Error performing photo ID scan:",e),e}}}},382:(e,t,o)=>{const s=new(o(706));e.exports={loadFaceTecSDK:()=>s.loadFaceTecSDK(),initializeFaceTec:(e,t)=>s.initializeFaceTec(e,t),getFaceTecVersion:()=>s.getFaceTecVersion()}},411:(e,t,o)=>{const s=o(599),n=o(641);e.exports={UuidGenerator:s,TokenStorage:n}},518:(e,t,o)=>{const s=o(719);e.exports=class{constructor(){this.baseUrl="/api"}async postIDScanOnly(e,t={},o=null){return new Promise(((n,r)=>{const c=performance.now();try{const a=`${this.baseUrl}/idscan-only`;s.logApiCall(a,"POST","Starting request");const i=new XMLHttpRequest;o&&"function"==typeof o&&(i.upload.onprogress=function(e){if(e.lengthComputable){const t=e.loaded/e.total;s.logIDScanProgress("Uploading",t),o(t)}}),i.onreadystatechange=function(){if(i.readyState===XMLHttpRequest.DONE){const e=performance.now();try{if(i.status>=200&&i.status<300){const t=JSON.parse(i.responseText);s.logPerformance("IDScanDataSource.postIDScanOnly",c,e),s.logApiCall(a,"POST",`Success (${i.status})`),s.logData("API Response",{status:i.status,wasProcessed:t.wasProcessed,error:t.error,hasScanResultBlob:!!t.scanResultBlob}),n(t)}else s.logPerformance("IDScanDataSource.postIDScanOnly (failed)",c,e),s.logError(`API call failed with status ${i.status}`),r(new Error(`HTTP error! status: ${i.status}`))}catch(e){s.logError("Failed to parse API response",e),r(new Error("Failed to parse response JSON"))}}},i.onerror=function(){const e=performance.now();s.logPerformance("IDScanDataSource.postIDScanOnly (network error)",c,e),s.logError("Network request failed"),r(new Error("Network request failed"))},i.open("POST",a),i.setRequestHeader("Content-Type","application/json"),Object.keys(t).forEach((e=>{void 0!==t[e]&&i.setRequestHeader(e,t[e])}));const l=JSON.stringify(e);s.logMessage(`Sending request to ${a} with ${Object.keys(e).length} data fields`),i.send(l)}catch(e){s.logError("IDScanDataSource - postIDScanOnly error",e),r(e)}}))}}},548:(e,t,o)=>{const s=o(599),{TokenStorage:n}=o(411);e.exports=class{async getSessionToken(e={}){try{const t="/api/session-token",o=e["X-Ekyc-Device-Info"]?null:s.getDeviceId(),r=s.getUniqueId(),c=e["X-Session-Id"]||s.getUniqueId();e["X-Session-Id"]||n.storeSessionId(c);const a=s.getUniqueId();e.Authorization&&(i.Authorization=e.Authorization);const i={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":"1.0.0","X-Ekyc-Device-Info":`browser|${o}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,"X-Session-Id":`${c}`,"X-Tid":`${r}`,correlationid:`${a}`,...e},l=await fetch(t,{method:"GET",headers:i});if(!l.ok)throw new Error(`API request failed with status ${l.status}`);return await l.json()}catch(e){throw console.error("Error getting session token:",e),e}}async getFaceTecSessionToken(e={}){try{const t="/api/facetec-session-token",o=e["X-Ekyc-Device-Info"]?null:s.getDeviceId(),r=s.getUniqueId(),c=n.getSessionId(),a={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":"1.0.0","X-Ekyc-Device-Info":`browser|${o}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,"X-Session-Id":`${e["X-Session-Id"]||c||s.getUniqueId()}`,"X-Tid":`${r}`,correlationid:`${s.getUniqueId()}`,...e};e.Authorization&&(a.Authorization=e.Authorization);const i=await fetch(t,{method:"GET",headers:a});if(!i.ok)throw new Error(`API request failed with status ${i.status}`);return await i.json()}catch(e){throw console.error("Error getting FaceTec session token:",e),e}}async getFaceTecSessionTokenWithEkycToken(e={}){try{const t="/api/facetec-session-token",o=n.getEkycToken();if(!o)throw new Error("No eKYC token found. Please get a session token first.");const r=e["X-Ekyc-Device-Info"]?null:s.getDeviceId(),c=e["X-Tid"]||s.getUniqueId(),a=n.getSessionId(),i=e["X-Session-Id"]||a||s.getUniqueId(),l=e.correlationid||s.getUniqueId(),u={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":e["X-Ekyc-Sdk-Version"]||"1.0.0","X-Ekyc-Device-Info":e["X-Ekyc-Device-Info"]||`browser|${r}`,"X-Session-Id":`${i}`,"X-Tid":`${c}`,correlationid:`${l}`,"X-Ekyc-Token":o,...e};e.Authorization&&(u.Authorization=e.Authorization);const d=await fetch(t,{method:"GET",headers:u});if(!d.ok)throw new Error(`API request failed with status ${d.status}`);return await d.json()}catch(e){throw console.error("Error getting FaceTec session token with eKYC token:",e),e}}}},592:(e,t,o)=>{const s=o(189);e.exports=class{execute(e,t="USD"){return new s(e,t).format()}}},599:e=>{e.exports=class{static generateUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}static getDeviceId(){if("undefined"!=typeof window&&window.localStorage){let e=localStorage.getItem("ekyc_device_id");return e||(e=this.generateUuid(),localStorage.setItem("ekyc_device_id",e)),e}return this.generateUuid()}static getUniqueId(){return this.generateUuid()}}},641:e=>{e.exports=class{static storeToken(e,t){if("undefined"!=typeof window&&window.localStorage&&t)try{return localStorage.setItem(e,t),!0}catch(e){return console.error("Error storing token:",e),!1}return!1}static getToken(e){return"undefined"!=typeof window&&window.localStorage?localStorage.getItem(e):null}static removeToken(e){if("undefined"!=typeof window&&window.localStorage)try{return localStorage.removeItem(e),!0}catch(e){return console.error("Error removing token:",e),!1}return!1}static storeEkycToken(e){return this.storeToken("ekyc_token",e)}static getEkycToken(){return this.getToken("ekyc_token")}static removeEkycToken(){return this.removeToken("ekyc_token")}static storeSessionId(e){return this.storeToken("ekyc_session_id",e)}static getSessionId(){return this.getToken("ekyc_session_id")}static removeSessionId(){return this.removeToken("ekyc_session_id")}}},706:e=>{e.exports=class{loadFaceTecSDK(){return new Promise(((e,t)=>{if("undefined"!=typeof window&&window.FaceTecSDK)return void e(window.FaceTecSDK);const o=document.createElement("script");o.src="/core-sdk/FaceTecSDK.js/FaceTecSDK.js",o.async=!0,o.onload=()=>{window.FaceTecSDK?e(window.FaceTecSDK):t(new Error("FaceTecSDK not found after loading script"))},o.onerror=()=>{t(new Error("Failed to load FaceTecSDK script"))},document.head.appendChild(o)}))}async initializeFaceTec(e,t){try{const o=await this.loadFaceTecSDK();return o.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),o.setImagesDirectory("/core-sdk/FaceTec_images"),new Promise(((s,n)=>{o.initializeInDevelopmentMode(e,t,(e=>{e?(console.log("FaceTecSDK initialized successfully"),s(!0)):(console.error("FaceTecSDK failed to initialize"),n(new Error("FaceTecSDK failed to initialize")))}))}))}catch(e){throw console.error("Error loading FaceTecSDK:",e),e}}async getFaceTecVersion(){try{return(await this.loadFaceTecSDK()).version()}catch(e){throw console.error("Error getting FaceTecSDK version:",e),e}}}},719:e=>{e.exports=class{static logMessage(e,t="info"){const o=`[${(new Date).toISOString()}] [${t.toUpperCase()}]`;switch(t){case"error":console.error(`${o} ${e}`);break;case"warn":console.warn(`${o} ${e}`);break;case"success":console.log(`%c${o} ${e}`,"color: green; font-weight: bold;");break;default:console.log(`${o} ${e}`)}}static logFaceTecStatus(e){this.logMessage(`FaceTec SDK: ${e}`,"info")}static logApiCall(e,t,o){this.logMessage(`API ${t} ${e}: ${o}`,"info")}static logSuccess(e){this.logMessage(e,"success")}static logError(e,t=null){let o=e;t&&(o+=` - ${t.message}`),this.logMessage(o,"error"),t&&t.stack&&console.error("Stack trace:",t.stack)}static logWarning(e){this.logMessage(e,"warn")}static logIDScanProgress(e,t=null){let o=`ID Scan: ${e}`;null!==t&&(o+=` (${Math.round(100*t)}%)`),this.logMessage(o,"info")}static logSession(e,t){this.logMessage(`Session ${e}: ${t}`,"info")}static clearConsole(){"function"==typeof console.clear&&console.clear()}static logData(e,t){console.group(`📊 ${e}`),console.log(t),console.groupEnd()}static logPerformance(e,t,o){const s=o-t;this.logMessage(`Performance: ${e} took ${s.toFixed(2)}ms`,"info")}}},863:e=>{e.exports=class{execute(e){return`Hello 12, ${e}!`}}},955:(e,t,o)=>{const s=o(0),n=o(719);e.exports=function(e,t,o,r){var c=this;this.latestNetworkRequest=new XMLHttpRequest,this.deviceKey=o||null,this.additionalHeaders=r||{},this.postIDScanOnlyUseCase=new s,this.processIDScanResultWhileFaceTecSDKWaits=function(e,t){if(c.latestIDScanResult=e,e.status!==FaceTecSDK.FaceTecIDScanStatus.Success)return c.latestNetworkRequest.abort(),c.latestNetworkRequest=new XMLHttpRequest,void t.cancel();c.executeIDScanUseCase(e,t)},this.executeIDScanUseCase=async function(e,t){try{const o=function(e){t.uploadProgress(e)},s=await c.postIDScanOnlyUseCase.execute({idScanResult:e,deviceKey:c.deviceKey,additionalHeaders:c.additionalHeaders,onProgress:o});s.success?(FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Front Scan Complete","Front of ID<br/>Scanned","ID Scan Complete","Passport Scan Complete","Photo ID Scan<br/>Complete","ID Photo Capture<br/>Complete","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"),t.proceedToNextStep(s.scanResultBlob)):c.cancelDueToNetworkError(s.errorMessage||"Unexpected API response, cancelling out.",t)}catch(e){console.error("PhotoIDScanProcessor - executeIDScanUseCase error:",e),c.cancelDueToNetworkError(e.message||"Exception while handling API response, cancelling out.",t)}},this.onFaceTecSDKCompletelyDone=function(){null!==c.latestIDScanResult&&(c.success=c.latestIDScanResult.isCompletelyDone),c.success&&n.logMessage("Id Scan Complete"),c.sampleAppControllerReference.onComplete(null,c.latestIDScanResult,200)},this.cancelDueToNetworkError=function(e,t){!1===c.cancelledDueToNetworkError&&(console.error(e),c.cancelledDueToNetworkError=!0,t.cancel())},this.isSuccess=function(){return c.success},this.success=!1,this.sampleAppControllerReference=t,this.latestIDScanResult=null,this.cancelledDueToNetworkError=!1,FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides("Uploading<br/>Encrypted<br/>ID Scan","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>ID Scan","Uploading<br/>Encrypted<br/>Back of ID","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>Back of ID","Uploading<br/>Your Confirmed Info","Still Uploading...<br/>Slow Connection","Info Saved","Processing"),new FaceTecSDK.FaceTecSession(this,e)}},985:e=>{e.exports=class{constructor(e){this.authRepository=e}async execute(e={}){return await this.authRepository.getFaceTecSessionTokenWithEkycToken(e)}}},995:e=>{e.exports=class{constructor(e){this.authRepository=e}async execute(e={}){return await this.authRepository.getSessionToken(e)}}}},t={},function o(s){var n=t[s];if(void 0!==n)return n.exports;var r=t[s]={exports:{}};return e[s](r,r.exports,o),r.exports}(347);var e,t}));