!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("api",[],t):"object"==typeof exports?exports.api=t():e.api=t()}(this,(()=>{return e={299:e=>{e.exports={getSessionToken:async(e={})=>{try{const t="/api/session-token",o={"Content-Type":"application/json",Accept:"application/json",...e},r=await fetch(t,{method:"GET",headers:o});if(!r.ok)throw new Error(`API request failed with status ${r.status}`);return await r.json()}catch(e){throw console.error("Error getting session token:",e),e}}}}},t={},function o(r){var n=t[r];if(void 0!==n)return n.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,o),s.exports}(299);var e,t}));