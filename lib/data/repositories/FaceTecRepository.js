const IDScanDataSource = require('../datasources/IDScanDataSource');

/**
 * Repository for FaceTec related operations
 * Implements the repository pattern to abstract data access
 */
class FaceTecRepository {
    constructor() {
        this.idScanDataSource = new IDScanDataSource();
    }

    /**
     * Submit ID scan data for processing
     * @param {Object} scanData - The scan data including images and metadata
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - Processed response
     */
    async submitIDScan(scanData, headers = {}, onProgress = null) {
        try {
            // Call the data source to post ID scan data
            const response = await this.idScanDataSource.postIDScanOnly(scanData, headers, onProgress);
            
            // Repository can add additional business logic here if needed
            // For example: data transformation, caching, etc.
            
            return response;
        } catch (error) {
            console.error('FaceTecRepository - submitIDScan error:', error);
            throw error;
        }
    }

    /**
     * Validate scan data before submission
     * @param {Object} scanData - The scan data to validate
     * @returns {boolean} - True if valid, throws error if invalid
     */
    validateScanData(scanData) {
        if (!scanData) {
            throw new Error('Scan data is required');
        }

        if (!scanData.idScan) {
            throw new Error('ID scan data is required');
        }

        // Add more validation rules as needed
        return true;
    }
}

module.exports = FaceTecRepository; 