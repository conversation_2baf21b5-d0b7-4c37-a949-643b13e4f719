{"name": "ekyc-browser-demo", "version": "0.0.0", "description": "techx-ekyc", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "build:lib": "webpack --mode production", "start": "next start -p 8080 -H 0.0.0.0", "lint": "next lint", "install:global": "echo \"Error: no package specified\" && exit 0", "install:package": "npm install", "test:coverage": "jest --coverage --detect<PERSON><PERSON><PERSON>andles --passWithNoTests && nyc report --reporter=text-lcov > coverage.lcov && nyc report --reporter=cobertura > coverage.xml"}, "dependencies": {"@babel/runtime": "^7.27.0", "@types/cls-hooked": "^4.3.3", "dotenv": "^16.0.3", "express": "^4.18.2", "express-actuator": "^1.8.4", "express-prometheus-middleware": "^1.2.0", "next": "^14.1.0", "prom-client": "^13.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "supertest": "^6.3.3"}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.22.20", "@babel/preset-react": "^7.22.15", "babel-loader": "^9.1.3", "copy-webpack-plugin": "^13.0.0", "css-loader": "^6.8.1", "eslint": "^8.32.0", "eslint-junit": "^1.0.1", "jest": "^29.3.1", "jest-junit": "^15.0.0", "nyc": "^15.1.0", "style-loader": "^3.3.3", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "nyc": {"report-dir": "./coverage", "temp-dir": "./.nyc_output", "reporter": ["text", "text-lcov", "cobertura"], "extension": [".js"]}, "jest": {"testEnvironment": "node", "collectCoverage": true, "coverageDirectory": "coverage", "coverageReporters": ["json", "lcov", "text", "cobertura"]}}