import React, { useState, useEffect } from 'react';
// This import will use the compiled code from webpack
// Note: This will only work after the lib has been built with webpack
// and the Next.js app has been configured to use the compiled code

export default function Home() {
  // We'll add a dynamic import for the library functions
  const [lib, setLib] = React.useState(null);
  const [error, setError] = React.useState(null);
  const [amount, setAmount] = React.useState(1234.56);
  const [name, setName] = React.useState('World');
  const [sessionToken, setSessionToken] = useState(null);
  const [sessionLoading, setSessionLoading] = useState(false);
  const [sessionError, setSessionError] = useState(null);
  const [faceTecSessionToken, setFaceTecSessionToken] = useState(null);
  const [faceTecSessionLoading, setFaceTecSessionLoading] = useState(false);
  const [faceTecSessionError, setFaceTecSessionError] = useState(null);
  const [apiKey, setApiKey] = useState('');
  const [deviceId, setDeviceId] = useState('');
  const [sessionId, setSessionId] = useState(null);
  // We don't need these states anymore as FaceTec initialization is handled in the library
  // But we'll keep track of the initialization status from the response
  const [facetecInitialized, setFacetecInitialized] = useState(false);
  const [facetecError, setFacetecError] = useState(null);

  // Add these states for the ID scan
  const [scanning, setScanning] = useState(false);
  const [scanResult, setScanResult] = useState(null);
  const [scanError, setScanError] = useState(null);

  // Load the main library
  useEffect(() => {
    // Dynamically import the library from the compiled code
    import('@lib').then(module => {
      console.log('Loaded library:', module);
      setLib(module);

      // Generate a UUID directly in the client-side code
      // This is a simpler approach that doesn't require importing the utils module
      const generateUuid = () => {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
          const r = Math.random() * 16 | 0;
          const v = c === 'x' ? r : (r & 0x3 | 0x8);
          return v.toString(16);
        });
      };

      // Get or create a device ID from localStorage
      const getDeviceId = () => {
        if (typeof window !== 'undefined' && window.localStorage) {
          let deviceId = localStorage.getItem('ekyc_device_id');
          if (!deviceId) {
            deviceId = generateUuid();
            localStorage.setItem('ekyc_device_id', deviceId);
          }
          return deviceId;
        } else {
          return generateUuid();
        }
      };

      // Set the device ID in the state
      const generatedDeviceId = getDeviceId();
      setDeviceId(generatedDeviceId);
    }).catch(err => {
      console.error('Failed to load library:', err);
      setError('Failed to load library. Make sure to run webpack build first.');
    });
  }, []);

  // We don't need to load the FaceTec library separately anymore
  // as it's now handled within the simple.js library

  // Function to handle getting the session token
  const handleGetSessionToken = async () => {
    if (!lib) return;

    setSessionLoading(true);
    setSessionToken(null);
    setSessionError(null);
    setSessionId(null);

    try {
      // Generate a session ID to use consistently across API calls
      const generateUuid = () => {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
          const r = Math.random() * 16 | 0;
          const v = c === 'x' ? r : (r & 0x3 | 0x8);
          return v.toString(16);
        });
      };

      // Generate a new session ID for this session
      const newSessionId = generateUuid();
      setSessionId(newSessionId);

      // Prepare headers with API key if provided
      const headers = {
        'X-Session-Id': newSessionId
      };

      if (apiKey) {
        headers['Authorization'] = "Bearer " + apiKey;
      }

      // Add the device ID to the headers
      if (deviceId) {
        headers['X-Ekyc-Device-Info'] = `browser|${deviceId}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`;
      }

      // Call the getSessionToken function from the lib with custom headers
      const response = await lib.getSessionToken(headers);
      console.log('Session token response:', response);
      setSessionToken(response);
    } catch (err) {
      console.error('Error getting session token:', err);
      setSessionError(err.message || 'Failed to get session token');
    } finally {
      setSessionLoading(false);
    }
  };

  // Function to handle getting the FaceTec session token
  const handleGetFaceTecSessionToken = async () => {
    if (!lib) return;

    setFaceTecSessionLoading(true);
    setFaceTecSessionToken(null);
    setFaceTecSessionError(null);
    setFacetecInitialized(false);
    setFacetecError(null);

    try {
      // Prepare headers with API key if provided
      const headers = {};

      // Add the session ID to ensure consistency across API calls
      if (sessionId) {
        headers['X-Session-Id'] = sessionId;
      }

      if (apiKey) {
        headers['Authorization'] = "Bearer " + apiKey;
      }

      // Add the device ID to the headers
      if (deviceId) {
        headers['X-Ekyc-Device-Info'] = `browser|${deviceId}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`;
      }

      // Call the getFaceTecSessionTokenWithEkycToken function from the lib with custom headers
      // The function now handles FaceTec initialization internally
      const response = await lib.getFaceTecSessionTokenWithEkycToken(headers, true);
      console.log('FaceTec session token response:', response);
      setFaceTecSessionToken(response);

      // Update the FaceTec initialization status based on the response
      if (response.faceTecInitialized) {
        setFacetecInitialized(true);
      } else if (response.faceTecError) {
        setFacetecError(response.faceTecError);
      }
    } catch (err) {
      console.error('Error getting FaceTec session token:', err);
      setFaceTecSessionError(err.message || 'Failed to get FaceTec session token');
    } finally {
      setFaceTecSessionLoading(false);
    }
  };

  // Handle API key input change
  const handleApiKeyChange = (e) => {
    setApiKey(e.target.value);
  };

  const handleAmountChange = (e) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value)) {
      setAmount(value);
    }
  };

  const handleNameChange = (e) => {
    setName(e.target.value);
  };

  const handleScanID = async () => {
    if (!lib || !facetecInitialized) {
      alert('Please initialize FaceTec SDK first');
      return;
    }

    setScanning(true);
    setScanResult(null);
    setScanError(null);

    try {
      // Prepare headers
      const headers = {};
      if (sessionId) {
        headers['X-Session-Id'] = sessionId;
      }
      if (apiKey) {
        headers['Authorization'] = "Bearer " + apiKey;
      }
      if (deviceId) {
        headers['X-Ekyc-Device-Info'] = `browser|${deviceId}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`;
      }

      const result = await lib.performPhotoIDScan(headers,deviceId,faceTecSessionToken);
      console.log('Scan result:', result);
      setScanResult(result);
    } catch (err) {
      console.error('Scan error:', err);
      setScanError(err.message || 'Failed to scan ID');
    } finally {
      setScanning(false);
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>eKYC Browser Demo</h1>
      <p>This is a Next.js app using functions compiled with webpack.</p>

      {error && (
        <div style={{ color: 'red', marginTop: '20px' }}>
          <p>{error}</p>
        </div>
      )}

      {/* Session Token Demo Section */}
      {lib && (
        <div style={{
          marginTop: '30px',
          padding: '20px',
          border: '1px solid #ddd',
          borderRadius: '5px',
          backgroundColor: '#f9f9f9'
        }}>
          <h2>Session Token API Demo</h2>
          <p>This demonstrates calling the GET /v1/ekyc/authen/sessiontoken endpoint with headers only.</p>

          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px' }}>
              Custom API Key (X-API-Key header):
              <input
                type="text"
                value={apiKey}
                onChange={handleApiKeyChange}
                placeholder="Enter your API key"
                style={{
                  marginLeft: '10px',
                  padding: '5px',
                  width: '300px'
                }}
              />
            </label>
            <p style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
              Leave empty to use the default API key from environment variables.
            </p>
          </div>

          <div style={{ marginBottom: '15px' }}>
            <h4 style={{ marginBottom: '5px' }}>Device ID Information:</h4>
            <p>
              Your device ID: <strong>{deviceId}</strong>
            </p>
            <p style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
              This UUID is stored in localStorage and used in the X-Ekyc-Device-Info header as "browser|UUID".
            </p>
          </div>

          {sessionId && (
            <div style={{ marginBottom: '15px' }}>
              <h4 style={{ marginBottom: '5px' }}>Session ID Information:</h4>
              <p>
                Current session ID: <strong>{sessionId}</strong>
              </p>
              <p style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
                This UUID is generated for this session and used in the X-Session-Id header for all API calls.
              </p>
            </div>
          )}

          <button
            onClick={handleGetSessionToken}
            disabled={sessionLoading}
            style={{
              padding: '10px 15px',
              marginTop: '10px',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: sessionLoading ? 'not-allowed' : 'pointer',
              opacity: sessionLoading ? 0.7 : 1
            }}
          >
            {sessionLoading ? 'Loading...' : 'Get Session Token'}
          </button>

          {sessionError && (
            <div style={{ marginTop: '15px', color: 'red' }}>
              <h3>Error</h3>
              <p>{sessionError}</p>
            </div>
          )}

          {sessionToken && (
            <div style={{ marginTop: '15px' }}>
              <h3>Response</h3>
              <pre style={{
                backgroundColor: '#f5f5f5',
                padding: '10px',
                borderRadius: '5px',
                overflow: 'auto',
                maxHeight: '300px'
              }}>
                {JSON.stringify(sessionToken, null, 2)}
              </pre>

              <div style={{ marginTop: '20px' }}>
                <h3>Get FaceTec Session Token</h3>
                <p>Now you can get a FaceTec session token using the stored eKYC token.</p>
                <p style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
                  Using session ID: <strong>{sessionId}</strong> for this API call to maintain session consistency.
                </p>

                <button
                  onClick={handleGetFaceTecSessionToken}
                  disabled={faceTecSessionLoading}
                  style={{
                    padding: '10px 15px',
                    marginTop: '10px',
                    backgroundColor: '#2196F3',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: faceTecSessionLoading ? 'not-allowed' : 'pointer',
                    opacity: faceTecSessionLoading ? 0.7 : 1
                  }}
                >
                  {faceTecSessionLoading ? 'Loading...' : 'Get FaceTec Session Token'}
                </button>

                {faceTecSessionError && (
                  <div style={{ marginTop: '15px', color: 'red' }}>
                    <h4>Error</h4>
                    <p>{faceTecSessionError}</p>
                  </div>
                )}

                {faceTecSessionToken && (
                  <div style={{ marginTop: '15px' }}>
                    <h4>FaceTec Session Token Response</h4>
                    <pre style={{
                      backgroundColor: '#f5f5f5',
                      padding: '10px',
                      borderRadius: '5px',
                      overflow: 'auto',
                      maxHeight: '300px'
                    }}>
                      {JSON.stringify(faceTecSessionToken, null, 2)}
                    </pre>

                    <div style={{ marginTop: '15px' }}>
                      <h4>FaceTec Initialization Status</h4>
                      {facetecInitialized && (
                        <p style={{ color: '#4CAF50' }}>
                          <strong>FaceTec SDK initialized successfully!</strong>
                        </p>
                      )}

                      {facetecError && (
                        <p style={{ color: 'red' }}>
                          <strong>Error initializing FaceTec SDK:</strong> {facetecError}
                        </p>
                      )}

                      {!facetecInitialized && !facetecError && (
                        <p style={{ color: '#666' }}>
                          <strong>FaceTec SDK initialization status will be shown here.</strong>
                        </p>
                      )}

                      <p style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
                        Using the following values from the response:
                        <br />
                        - Device Key: <strong>{faceTecSessionToken.data?.deviceKey?.substring(0, 10)}...</strong>
                        <br />
                        - Encryption Key: <strong>{faceTecSessionToken.data?.encryptionKey?.substring(0, 10)}...</strong>
                        <br />
                        - Session Token: <strong>{faceTecSessionToken.data?.sessionFaceTec?.substring(0, 10)}...</strong>
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* ID Scan Section */}
      {true && (
        <div style={{
          marginTop: '30px',
          padding: '20px',
          border: '1px solid #ddd',
          borderRadius: '5px',
          backgroundColor: '#f9f9f9'
        }}>
          <h2>ID Card Scan Demo</h2>
          <p>Scan your ID card or passport using FaceTec SDK.</p>

          <button
            onClick={handleScanID}
            disabled={scanning}
            style={{
              padding: '10px 20px',
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: scanning ? 'not-allowed' : 'pointer',
              opacity: scanning ? 0.7 : 1
            }}
          >
            {scanning ? 'Scanning...' : 'Scan ID Card'}
          </button>

          {scanError && (
            <div style={{ color: 'red', marginTop: '10px' }}>
              <p>{scanError}</p>
            </div>
          )}

          {scanResult && (
            <div style={{ marginTop: '10px' }}>
              <h4>Scan Result:</h4>
              <pre style={{ 
                backgroundColor: '#f5f5f5', 
                padding: '10px', 
                borderRadius: '5px',
                overflow: 'auto'
              }}>
                {JSON.stringify(scanResult, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}

      {!lib && !error && (
        <div style={{ marginTop: '20px' }}>
          <p>Loading library...</p>
        </div>
      )}
    </div>
  );
}
